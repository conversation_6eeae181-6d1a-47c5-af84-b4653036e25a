"use client"

import { useState } from "react"
import { Users, Package, BarChart3, DollarSign, ShoppingCart, Home } from "lucide-react"
import { mockProducts, mockUsers, mockOrders } from "../data/mockData"
import ProductCRUD from "./components/ProductCRUD"
import UserManagement from "./components/UserManagement"
import HomepageManagement from "./components/HomepageManagement"

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState("overview")

  // TODO: Replace with actual admin authentication check
  const isAdmin = true

  if (!isAdmin) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-2xl font-bold mb-4">الوصول مرفوض</h1>
        <p className="text-gray-400">ليس لديك صلاحية للوصول إلى هذه الصفحة.</p>
      </div>
    )
  }

  const tabs = [
    { id: "overview", label: "نظرة عامة", icon: BarChart3 },
    { id: "products", label: "المنتجات", icon: Package },
    { id: "users", label: "المستخدمون", icon: Users },
    { id: "homepage", label: "الصفحة الرئيسية", icon: Home },
  ]

  const stats = {
    totalProducts: mockProducts.length,
    totalUsers: mockUsers.length,
    totalOrders: mockOrders.length,
    totalRevenue: mockOrders.reduce((sum, order) => sum + order.amount, 0),
    pendingOrders: mockOrders.filter((o) => o.status === "pending").length,
    completedOrders: mockOrders.filter((o) => o.status === "completed").length,
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
          لوحة التحكم الإدارية
        </h1>
        <p className="text-gray-400 text-lg">إدارة متجرك والمنتجات والمستخدمين والصفحة الرئيسية</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar Navigation */}
        <div className="lg:col-span-1">
          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 p-4 shadow-xl">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 space-x-reverse px-4 py-3 rounded-lg transition-all duration-300 ${
                      activeTab === tab.id
                        ? "bg-purple-600 text-white shadow-lg shadow-purple-500/25"
                        : "text-gray-400 hover:text-white hover:bg-gray-700/50"
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{tab.label}</span>
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {activeTab === "overview" && (
            <div className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700/50 shadow-xl hover:shadow-purple-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">إجمالي المنتجات</p>
                      <p className="text-2xl font-bold">{stats.totalProducts}</p>
                    </div>
                    <Package className="w-8 h-8 text-purple-400" />
                  </div>
                </div>

                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700/50 shadow-xl hover:shadow-blue-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">إجمالي المستخدمين</p>
                      <p className="text-2xl font-bold">{stats.totalUsers}</p>
                    </div>
                    <Users className="w-8 h-8 text-blue-400" />
                  </div>
                </div>

                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700/50 shadow-xl hover:shadow-green-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">إجمالي الطلبات</p>
                      <p className="text-2xl font-bold">{stats.totalOrders}</p>
                    </div>
                    <ShoppingCart className="w-8 h-8 text-green-400" />
                  </div>
                </div>

                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700/50 shadow-xl hover:shadow-yellow-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">إجمالي الإيرادات</p>
                      <p className="text-2xl font-bold">${stats.totalRevenue.toFixed(2)}</p>
                    </div>
                    <DollarSign className="w-8 h-8 text-yellow-400" />
                  </div>
                </div>
              </div>

              {/* Recent Orders */}
              <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
                <div className="p-6 border-b border-gray-700/50">
                  <h2 className="text-xl font-bold">الطلبات الأخيرة</h2>
                </div>
                <div className="p-6">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="text-right text-gray-400 text-sm">
                          <th className="pb-3">رقم الطلب</th>
                          <th className="pb-3">المنتج</th>
                          <th className="pb-3">المبلغ</th>
                          <th className="pb-3">الحالة</th>
                          <th className="pb-3">التاريخ</th>
                        </tr>
                      </thead>
                      <tbody className="space-y-2">
                        {mockOrders.slice(0, 5).map((order) => {
                          const product = mockProducts.find((p) => p.id === order.productId)
                          return (
                            <tr key={order.id} className="border-t border-gray-700/50">
                              <td className="py-3 font-mono text-sm">#{order.id}</td>
                              <td className="py-3">{product?.title || "غير معروف"}</td>
                              <td className="py-3 font-semibold">${order.amount}</td>
                              <td className="py-3">
                                <span
                                  className={`px-2 py-1 rounded-lg text-xs ${
                                    order.status === "completed"
                                      ? "bg-green-400/10 text-green-400"
                                      : order.status === "pending"
                                        ? "bg-yellow-400/10 text-yellow-400"
                                        : "bg-red-400/10 text-red-400"
                                  }`}
                                >
                                  {order.status === "completed"
                                    ? "مكتمل"
                                    : order.status === "pending"
                                      ? "قيد الانتظار"
                                      : "فاشل"}
                                </span>
                              </td>
                              <td className="py-3 text-sm text-gray-400">
                                {new Date(order.createdAt).toLocaleDateString("ar")}
                              </td>
                            </tr>
                          )
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "products" && <ProductCRUD />}

          {activeTab === "users" && <UserManagement />}

          {activeTab === "homepage" && <HomepageManagement />}
        </div>
      </div>
    </div>
  )
}
