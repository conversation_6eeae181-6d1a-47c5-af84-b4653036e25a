@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 262.1 83.3% 57.8%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262.1 83.3% 57.8%;

    /* Modern Navbar Custom Properties */
    --navbar-height: 4rem;
    --navbar-blur: 20px;
    --navbar-bg-primary: rgba(17, 24, 39, 0.85);
    --navbar-bg-secondary: rgba(31, 41, 55, 0.75);
    --navbar-border: rgba(75, 85, 99, 0.2);
    --navbar-border-hover: rgba(139, 92, 246, 0.3);
    --navbar-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    --navbar-shadow-hover: 0 12px 40px rgba(139, 92, 246, 0.15);

    /* Animation Timing */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
    --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
    --ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Glassmorphism Properties */
    --glass-bg-light: rgba(255, 255, 255, 0.05);
    --glass-bg-medium: rgba(255, 255, 255, 0.08);
    --glass-bg-strong: rgba(255, 255, 255, 0.12);
    --glass-border-light: rgba(255, 255, 255, 0.1);
    --glass-border-medium: rgba(255, 255, 255, 0.15);
    --glass-shadow-light: 0 4px 16px rgba(0, 0, 0, 0.1);
    --glass-shadow-medium: 0 8px 32px rgba(0, 0, 0, 0.15);
    --glass-shadow-strong: 0 16px 64px rgba(0, 0, 0, 0.2);
  }
}

@layer components {
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .card-hover {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-purple-500/20;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 shadow-lg hover:shadow-purple-500/25;
  }

  .btn-secondary {
    @apply bg-gray-700/50 backdrop-blur-sm hover:bg-gray-600/50 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 border border-gray-600/50;
  }

  .glass-effect {
    @apply bg-gray-800/50 backdrop-blur-md border border-gray-700/50;
  }

  /* Modern Navbar Classes */
  .navbar-modern {
    background: var(--navbar-bg-primary);
    backdrop-filter: blur(var(--navbar-blur));
    border-bottom: 1px solid var(--navbar-border);
    box-shadow: var(--navbar-shadow);
    transition: all var(--transition-normal) var(--ease-out-quart);
  }

  .navbar-modern:hover {
    background: var(--navbar-bg-secondary);
    border-bottom-color: var(--navbar-border-hover);
    box-shadow: var(--navbar-shadow-hover);
  }

  .nav-link-modern {
    @apply relative px-4 py-2 rounded-lg font-medium transition-all;
    transition-duration: var(--transition-normal);
    transition-timing-function: var(--ease-out-quart);
  }

  .nav-link-modern::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--glass-bg-light);
    border-radius: inherit;
    opacity: 0;
    transition: opacity var(--transition-fast) var(--ease-out-quart);
  }

  .nav-link-modern:hover::before {
    opacity: 1;
  }

  .nav-link-modern::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #8b5cf6, #3b82f6);
    border-radius: 1px;
    transform: translateX(-50%);
    transition: width var(--transition-normal) var(--ease-spring);
  }

  .nav-link-modern:hover::after {
    width: 80%;
  }

  .glass-card-modern {
    background: var(--glass-bg-medium);
    backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border-light);
    box-shadow: var(--glass-shadow-medium);
    transition: all var(--transition-normal) var(--ease-out-quart);
  }

  .glass-card-modern:hover {
    background: var(--glass-bg-strong);
    border-color: var(--glass-border-medium);
    box-shadow: var(--glass-shadow-strong);
    transform: translateY(-2px);
  }

  /* Animation Classes */
  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-in-top {
    animation: slideInFromTop var(--transition-slow) var(--ease-out-quart);
  }

  .animate-scale-in {
    animation: scaleIn var(--transition-normal) var(--ease-spring);
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-stagger {
    animation: staggerFadeIn var(--transition-normal) var(--ease-out-quart);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modern Animation Keyframes */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(139, 92, 246, 0.5);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes staggerFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.7);
}

/* Enhanced Glassmorphism Effects */
.glass-card {
  background: rgba(31, 41, 55, 0.5);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.glass-card:hover {
  background: rgba(31, 41, 55, 0.6);
  border-color: rgba(139, 92, 246, 0.3);
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.1);
}

/* Advanced Glassmorphism Variants */
.glass-navbar {
  background: linear-gradient(
    135deg,
    rgba(17, 24, 39, 0.9) 0%,
    rgba(31, 41, 55, 0.8) 50%,
    rgba(17, 24, 39, 0.9) 100%
  );
  backdrop-filter: blur(20px) saturate(180%);
  border-bottom: 1px solid rgba(75, 85, 99, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.glass-navbar::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    rgba(139, 92, 246, 0.05) 0%,
    transparent 50%,
    rgba(59, 130, 246, 0.05) 100%
  );
  pointer-events: none;
}

.glass-button {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all var(--transition-normal) var(--ease-out-quart);
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(139, 92, 246, 0.3);
  box-shadow:
    0 8px 24px rgba(139, 92, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.glass-menu {
  background: rgba(17, 24, 39, 0.95);
  backdrop-filter: blur(24px) saturate(180%);
  border: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow:
    0 20px 64px rgba(0, 0, 0, 0.2),
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Responsive Design Utilities */
@media (max-width: 1023px) {
  .navbar-modern {
    padding: 0 1rem;
  }

  .nav-link-modern {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }
}

@media (max-width: 767px) {
  .navbar-modern {
    height: 3.5rem;
    padding: 0 0.75rem;
  }

  .glass-menu {
    margin: 0.5rem;
    padding: 1rem;
    border-radius: 1rem;
  }

  .nav-link-modern {
    font-size: 1rem;
    padding: 0.75rem 1rem;
  }
}

@media (max-width: 475px) {
  .navbar-modern {
    height: 3rem;
    padding: 0 0.5rem;
  }

  .glass-menu {
    margin: 0.25rem;
    padding: 0.75rem;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .nav-link-modern:hover::before {
    opacity: 0;
  }

  .nav-link-modern:active::before {
    opacity: 1;
  }

  .glass-button:hover {
    transform: none;
  }

  .glass-button:active {
    transform: scale(0.95);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-navbar {
    background: rgba(0, 0, 0, 0.9);
    border-bottom-color: rgba(255, 255, 255, 0.5);
  }

  .glass-button {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-float,
  .animate-glow,
  .animate-shimmer {
    animation: none;
  }

  .nav-link-modern,
  .glass-button,
  .navbar-modern {
    transition: none;
  }
}

/* Advanced Interaction Patterns */
.interactive-element {
  position: relative;
  overflow: hidden;
}

.interactive-element::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease-out, height 0.6s ease-out;
  pointer-events: none;
}

.interactive-element:active::before {
  width: 300px;
  height: 300px;
}

/* Focus States for Accessibility */
.nav-link-modern:focus-visible,
.glass-button:focus-visible,
button:focus-visible {
  outline: 2px solid rgba(139, 92, 246, 0.8);
  outline-offset: 2px;
  border-radius: 0.5rem;
}

/* Enhanced Hover Effects */
.hover-lift {
  transition: transform var(--transition-normal) var(--ease-out-quart);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-glow {
  transition: box-shadow var(--transition-normal) var(--ease-out-quart);
}

.hover-glow:hover {
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.3);
}

/* Micro-interactions */
.micro-bounce {
  transition: transform 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.micro-bounce:active {
  transform: scale(0.95);
}

.micro-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.05) 25%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s linear infinite;
}

/* State Indicators */
.state-active {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(59, 130, 246, 0.2));
  border-color: rgba(139, 92, 246, 0.4);
}

.state-active::after {
  width: 100% !important;
  background: linear-gradient(90deg, #8b5cf6, #3b82f6);
}

/* Tooltip Styles */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(17, 24, 39, 0.95);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition-normal) var(--ease-out-quart);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}
