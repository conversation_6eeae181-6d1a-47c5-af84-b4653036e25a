"use client"

import { useState } from "react"
import { Eye, <PERSON>, Ban, User<PERSON>he<PERSON>, X, Save } from "lucide-react"
import { mockUsers, mockOrders } from "../../data/mockData"
import type { User } from "../../types"

export default function UserManagement() {
  const [users, setUsers] = useState(mockUsers)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [isViewModalOpen, setIsViewModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editForm, setEditForm] = useState<Partial<User>>({})
  const [isLoading, setIsLoading] = useState(false)

  const openViewModal = (user: User) => {
    setSelectedUser(user)
    setIsViewModalOpen(true)
  }

  const openEditModal = (user: User) => {
    setSelectedUser(user)
    setEditForm(user)
    setIsEditModalOpen(true)
  }

  const closeModals = () => {
    setIsViewModalOpen(false)
    setIsEditModalOpen(false)
    setSelectedUser(null)
    setEditForm({})
  }

  const handleSaveUser = async () => {
    if (!selectedUser) return

    setIsLoading(true)
    // TODO: Implement with Supabase
    await new Promise((resolve) => setTimeout(resolve, 1000))

    setUsers((prev) => prev.map((user) => (user.id === selectedUser.id ? ({ ...user, ...editForm } as User) : user)))

    setIsLoading(false)
    closeModals()
  }

  const handleBanUser = async (userId: string) => {
    if (!confirm("هل أنت متأكد من حظر هذا المستخدم؟")) return

    setIsLoading(true)
    // TODO: Implement ban functionality with Supabase
    await new Promise((resolve) => setTimeout(resolve, 500))

    alert("تم حظر المستخدم بنجاح")
    setIsLoading(false)
  }

  const handlePromoteUser = async (userId: string, newRole: "admin" | "distributor" | "user") => {
    if (!confirm(`هل أنت متأكد من ترقية هذا المستخدم إلى ${newRole}؟`)) return

    setIsLoading(true)
    // TODO: Implement role change with Supabase
    await new Promise((resolve) => setTimeout(resolve, 500))

    setUsers((prev) => prev.map((user) => (user.id === userId ? { ...user, role: newRole } : user)))

    setIsLoading(false)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">إدارة المستخدمين</h2>
        <div className="text-sm text-gray-400">{users.length} مستخدم إجمالي</div>
      </div>

      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
        <div className="p-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-right text-gray-400 text-sm">
                  <th className="pb-3">المستخدم</th>
                  <th className="pb-3">الدور</th>
                  <th className="pb-3">رصيد المحفظة</th>
                  <th className="pb-3">الطلبات</th>
                  <th className="pb-3">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => {
                  const userOrders = mockOrders.filter((o) => o.userId === user.id)
                  return (
                    <tr key={user.id} className="border-t border-gray-700/50">
                      <td className="py-4">
                        <div>
                          <p className="font-semibold">{user.name}</p>
                          <p className="text-sm text-gray-400">{user.email}</p>
                        </div>
                      </td>
                      <td className="py-4">
                        <span
                          className={`px-2 py-1 rounded text-xs ${
                            user.role === "admin"
                              ? "bg-red-400/10 text-red-400"
                              : user.role === "distributor"
                                ? "bg-blue-400/10 text-blue-400"
                                : "bg-gray-400/10 text-gray-400"
                          }`}
                        >
                          {user.role === "admin" ? "مدير" : user.role === "distributor" ? "موزع" : "مستخدم"}
                        </span>
                      </td>
                      <td className="py-4 font-semibold">${user.walletBalance.toFixed(2)}</td>
                      <td className="py-4">{userOrders.length}</td>
                      <td className="py-4">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={() => openViewModal(user)}
                            className="p-2 text-gray-400 hover:text-blue-400 transition-colors rounded-lg hover:bg-blue-400/10"
                            title="عرض التفاصيل"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => openEditModal(user)}
                            className="p-2 text-gray-400 hover:text-purple-400 transition-colors rounded-lg hover:bg-purple-400/10"
                            title="تعديل"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleBanUser(user.id)}
                            className="p-2 text-gray-400 hover:text-red-400 transition-colors rounded-lg hover:bg-red-400/10"
                            title="حظر"
                            disabled={isLoading}
                          >
                            <Ban className="w-4 h-4" />
                          </button>
                          {user.role !== "admin" && (
                            <button
                              onClick={() => handlePromoteUser(user.id, user.role === "user" ? "distributor" : "admin")}
                              className="p-2 text-gray-400 hover:text-green-400 transition-colors rounded-lg hover:bg-green-400/10"
                              title="ترقية"
                              disabled={isLoading}
                            >
                              <UserCheck className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* View User Modal */}
      {isViewModalOpen && selectedUser && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800/90 backdrop-blur-md rounded-xl max-w-2xl w-full border border-gray-700/50 shadow-2xl">
            <div className="p-6 border-b border-gray-700/50">
              <div className="flex items-center justify-between">
                <h3 className="text-2xl font-bold">تفاصيل المستخدم</h3>
                <button onClick={closeModals} className="text-gray-400 hover:text-white">
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50">
                  <h4 className="font-semibold mb-2">الاسم</h4>
                  <p className="text-gray-300">{selectedUser.name}</p>
                </div>
                <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50">
                  <h4 className="font-semibold mb-2">البريد الإلكتروني</h4>
                  <p className="text-gray-300">{selectedUser.email}</p>
                </div>
                <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50">
                  <h4 className="font-semibold mb-2">الدور</h4>
                  <p className="text-gray-300">
                    {selectedUser.role === "admin" ? "مدير" : selectedUser.role === "distributor" ? "موزع" : "مستخدم"}
                  </p>
                </div>
                <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50">
                  <h4 className="font-semibold mb-2">رصيد المحفظة</h4>
                  <p className="text-green-400 font-bold text-lg">${selectedUser.walletBalance.toFixed(2)}</p>
                </div>
              </div>

              <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50">
                <h4 className="font-semibold mb-2">إحصائيات الطلبات</h4>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-blue-400">
                      {mockOrders.filter((o) => o.userId === selectedUser.id).length}
                    </p>
                    <p className="text-sm text-gray-400">إجمالي الطلبات</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-green-400">
                      {mockOrders.filter((o) => o.userId === selectedUser.id && o.status === "completed").length}
                    </p>
                    <p className="text-sm text-gray-400">مكتملة</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-yellow-400">
                      $
                      {mockOrders
                        .filter((o) => o.userId === selectedUser.id)
                        .reduce((sum, o) => sum + o.amount, 0)
                        .toFixed(2)}
                    </p>
                    <p className="text-sm text-gray-400">إجمالي الإنفاق</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {isEditModalOpen && selectedUser && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800/90 backdrop-blur-md rounded-xl max-w-md w-full border border-gray-700/50 shadow-2xl">
            <div className="p-6 border-b border-gray-700/50">
              <div className="flex items-center justify-between">
                <h3 className="text-2xl font-bold">تعديل المستخدم</h3>
                <button onClick={closeModals} className="text-gray-400 hover:text-white">
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">الاسم</label>
                <input
                  type="text"
                  value={editForm.name || ""}
                  onChange={(e) => setEditForm((prev) => ({ ...prev, name: e.target.value }))}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">البريد الإلكتروني</label>
                <input
                  type="email"
                  value={editForm.email || ""}
                  onChange={(e) => setEditForm((prev) => ({ ...prev, email: e.target.value }))}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">رصيد المحفظة</label>
                <input
                  type="number"
                  step="0.01"
                  value={editForm.walletBalance || 0}
                  onChange={(e) => setEditForm((prev) => ({ ...prev, walletBalance: Number(e.target.value) }))}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">الدور</label>
                <select
                  value={editForm.role || "user"}
                  onChange={(e) =>
                    setEditForm((prev) => ({ ...prev, role: e.target.value as "admin" | "distributor" | "user" }))
                  }
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                >
                  <option value="user">مستخدم</option>
                  <option value="distributor">موزع</option>
                  <option value="admin">مدير</option>
                </select>
              </div>

              <div className="flex space-x-4 space-x-reverse pt-4">
                <button
                  onClick={handleSaveUser}
                  disabled={isLoading}
                  className="flex-1 btn-primary flex items-center justify-center space-x-2 space-x-reverse"
                >
                  {isLoading ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  ) : (
                    <>
                      <Save className="w-5 h-5" />
                      <span>حفظ التغييرات</span>
                    </>
                  )}
                </button>
                <button onClick={closeModals} disabled={isLoading} className="flex-1 btn-secondary">
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
