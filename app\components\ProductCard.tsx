import Image from "next/image"
import Link from "next/link"
import { Star, MessageSquare, Key, AlertTriangle } from "lucide-react"
import type { Product } from "../types"

interface ProductCardProps {
  product: Product
  userRole?: "admin" | "distributor" | "user"
}

export default function ProductCard({ product, userRole = "user" }: ProductCardProps) {
  const cheapestPackage = product.packages.reduce(
    (min, pkg) => (pkg.price < min.price ? pkg : min),
    product.packages[0],
  )

  const hasDigitalCodes = product.packages.some((pkg) => pkg.hasDigitalCodes)
  const hasOutOfStockCodes = product.packages.some((pkg) => pkg.hasDigitalCodes && (pkg.availableCodesCount || 0) === 0)

  return (
    <Link href={`/product/${product.slug}`}>
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl overflow-hidden card-hover border border-gray-700/50 h-full flex flex-col shadow-xl">
        {/* Product Image */}
        <div className="relative">
          <Image
            src={product.coverImage || "/placeholder.svg"}
            alt={product.title}
            width={300}
            height={200}
            className="w-full h-32 sm:h-40 object-cover"
          />
          {cheapestPackage.discount && (
            <div className="absolute top-2 right-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white px-2 py-1 rounded-lg text-xs font-bold shadow-lg">
              {cheapestPackage.discount}% خصم
            </div>
          )}
          {product.featured && (
            <div className="absolute top-2 left-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white px-2 py-1 rounded-lg text-xs font-semibold shadow-lg">
              مميز
            </div>
          )}
          {hasDigitalCodes && (
            <div className="absolute bottom-2 right-2 bg-blue-600/90 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs font-semibold flex items-center space-x-1 space-x-reverse shadow-lg">
              <Key className="w-3 h-3" />
              <span>كود رقمي</span>
            </div>
          )}
          {hasOutOfStockCodes && (
            <div className="absolute bottom-2 left-2 bg-red-600/90 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs font-semibold flex items-center space-x-1 space-x-reverse shadow-lg">
              <AlertTriangle className="w-3 h-3" />
              <span>نفدت الأكواد</span>
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="p-3 sm:p-4 flex-1 flex flex-col">
          <h3 className="text-sm sm:text-base font-semibold mb-2 text-white line-clamp-2 flex-1">{product.title}</h3>

          {/* Rating and Comments */}
          <div className="flex items-center justify-center space-x-2 space-x-reverse mt-auto">
            <div className="flex items-center space-x-1 space-x-reverse">
              <Star className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-400 fill-current" />
              <span className="text-xs sm:text-sm text-gray-300 font-semibold">{product.rating}</span>
            </div>
            <div className="flex items-center space-x-1 space-x-reverse">
              <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4 text-gray-400" />
              <span className="text-xs sm:text-sm text-gray-300">{product.commentCount}</span>
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}
