"use client"

import Link from "next/link"
import { useState, useEffect } from "react"
import { User, Menu, X, Wallet, Shield, ChevronDown } from "lucide-react"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  // TODO: Replace with actual user data from Supabase
  const mockUser = {
    name: "أحمد محمد",
    walletBalance: 150.0,
    isLoggedIn: true,
    role: "admin", // Change this to test different roles
  }

  // Handle scroll effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMenuOpen && !(event.target as Element).closest('.mobile-menu-container')) {
        setIsMenuOpen(false)
      }
    }
    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [isMenuOpen])

  // Navigation items
  const navItems = [
    { href: "/", label: "الرئيسية", icon: null },
    { href: "/shop", label: "المتجر", icon: null },
    { href: "/wallet", label: "المحفظة", icon: null },
    { href: "/profile", label: "الملف الشخصي", icon: null },
  ]

  return (
    <header
      className="glass-navbar sticky top-0 z-50 h-16"
      role="banner"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Clean Logo */}
          <Link
            href="/"
            className="flex items-center space-x-3 space-x-reverse group"
            aria-label="متجر بنتاكون - الصفحة الرئيسية"
          >
            <div className="w-9 h-9 bg-gradient-to-br from-purple-600 to-blue-600 rounded-xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-shadow duration-200">
              <span className="text-white font-bold text-lg">ب</span>
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              متجر بنتاكون
            </span>
          </Link>

          {/* Clean Desktop Navigation */}
          <nav
            className="hidden lg:flex items-center space-x-6 space-x-reverse"
            role="navigation"
            aria-label="التنقل الرئيسي"
          >
            {navItems.filter(item => item.href !== "/profile").map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="nav-link-modern"
              >
                {item.label}
              </Link>
            ))}

            {/* Admin Panel Link */}
            {mockUser.role === "admin" && (
              <Link
                href="/admin"
                className="nav-link-modern flex items-center space-x-2 space-x-reverse glass-element px-3 py-1.5 rounded-xl"
                aria-label="لوحة التحكم - صفحة الإدارة"
              >
                <Shield className="w-4 h-4 text-purple-400" />
                <span>لوحة التحكم</span>
              </Link>
            )}
          </nav>

          {/* Clean User Actions */}
          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            {mockUser.isLoggedIn ? (
              <Link
                href="/profile"
                className="glass-element flex items-center space-x-3 space-x-reverse px-4 py-2 rounded-xl group hover:bg-gray-700/70 transition-all duration-200"
                aria-label="الملف الشخصي"
              >
                {/* User Avatar */}
                <div className="relative">
                  <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl flex items-center justify-center shadow-sm">
                    <User className="w-4 h-4 text-white" />
                  </div>
                  <div className="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 bg-green-400 rounded-full border border-gray-800" />
                </div>

                {/* User Info */}
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-gray-100 group-hover:text-white transition-colors duration-200">
                    {mockUser.name}
                  </span>
                  <div className="flex items-center space-x-1 space-x-reverse text-xs text-gray-400">
                    <Wallet className="w-3 h-3 text-emerald-400" />
                    <span>${mockUser.walletBalance.toFixed(2)}</span>
                  </div>
                </div>

                {/* Chevron */}
                <ChevronDown className="w-4 h-4 text-gray-400 group-hover:text-gray-300 transition-colors duration-200" />
              </Link>
            ) : (
              <button
                className="btn-primary px-6 py-2 rounded-xl font-medium text-sm transition-all duration-200 hover:shadow-lg"
                aria-label="تسجيل الدخول إلى الحساب"
              >
                تسجيل الدخول
              </button>
            )}
          </div>

          {/* Clean Mobile Menu Button */}
          <button
            className="lg:hidden glass-element p-2 rounded-xl transition-all duration-200 mobile-menu-container"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label={isMenuOpen ? "إغلاق القائمة" : "فتح القائمة"}
            aria-expanded={isMenuOpen}
            aria-controls="mobile-menu"
          >
            {isMenuOpen ? (
              <X className="w-6 h-6 text-gray-200" />
            ) : (
              <Menu className="w-6 h-6 text-gray-200" />
            )}
          </button>
        </div>

        {/* Clean Mobile Menu */}
        <div
          className={`
            lg:hidden overflow-hidden transition-all duration-300 mobile-menu-container
            ${isMenuOpen ? 'max-h-80 opacity-100' : 'max-h-0 opacity-0'}
          `}
          id="mobile-menu"
          role="navigation"
          aria-label="قائمة التنقل المحمول"
        >
          <div className="glass-menu mt-2 p-4 rounded-xl mx-4">
            <nav className="flex flex-col space-y-2">
              {navItems.filter(item => item.href !== "/profile").map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="text-gray-200 hover:text-white font-medium py-2 px-3 rounded-xl hover:bg-white/5 transition-colors duration-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}

              {/* Admin Panel Link for Mobile */}
              {mockUser.role === "admin" && (
                <Link
                  href="/admin"
                  className="text-gray-200 hover:text-white font-medium py-2 px-3 rounded-xl hover:bg-white/5 transition-colors duration-200 flex items-center space-x-2 space-x-reverse glass-element"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Shield className="w-4 h-4 text-purple-400" />
                  <span>لوحة التحكم</span>
                </Link>
              )}

              {/* User Profile Link for Mobile */}
              {mockUser.isLoggedIn && (
                <Link
                  href="/profile"
                  className="mt-3 pt-3 border-t border-gray-700/30 glass-element flex items-center space-x-3 space-x-reverse p-3 rounded-xl hover:bg-gray-700/70 transition-colors duration-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className="relative">
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
                      <User className="w-4 h-4 text-white" />
                    </div>
                    <div className="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 bg-green-400 rounded-full border border-gray-800" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-100">{mockUser.name}</span>
                      <div className="flex items-center space-x-1 space-x-reverse text-emerald-400">
                        <Wallet className="w-3 h-3" />
                        <span className="text-xs font-medium">${mockUser.walletBalance.toFixed(2)}</span>
                      </div>
                    </div>
                    <span className="text-xs text-gray-400">الملف الشخصي</span>
                  </div>
                </Link>
              )}
            </nav>
          </div>
        </div>
      </div>
    </header>
  )
}
