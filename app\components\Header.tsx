"use client"

import Link from "next/link"
import { useState, useEffect } from "react"
import { User, Menu, X, Wallet, Shield, ChevronDown } from "lucide-react"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  // TODO: Replace with actual user data from Supabase
  const mockUser = {
    name: "أحمد محمد",
    walletBalance: 150.0,
    isLoggedIn: true,
    role: "admin", // Change this to test different roles
  }

  // Handle scroll effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMenuOpen && !(event.target as Element).closest('.mobile-menu-container')) {
        setIsMenuOpen(false)
      }
    }
    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [isMenuOpen])

  // Navigation items
  const navItems = [
    { href: "/", label: "الرئيسية", icon: null },
    { href: "/shop", label: "المتجر", icon: null },
    { href: "/wallet", label: "المحفظة", icon: null },
    { href: "/profile", label: "الملف الشخصي", icon: null },
  ]

  return (
    <header
      className={`
        glass-navbar navbar-modern sticky top-0 z-50 transition-all duration-500 ease-out
        ${isScrolled ? 'h-14 shadow-2xl' : 'h-16 shadow-lg'}
      `}
      role="banner"
    >
      {/* Gradient overlay for enhanced glassmorphism */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 via-transparent to-blue-600/5 pointer-events-none" />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="flex items-center justify-between h-full">
          {/* Enhanced Logo */}
          <Link
            href="/"
            className="flex items-center space-x-3 space-x-reverse group animate-scale-in"
            aria-label="متجر بنتاكون - الصفحة الرئيسية"
          >
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-600 via-purple-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-purple-500/40 transition-all duration-500 animate-float">
                <span className="text-white font-bold text-xl">ب</span>
              </div>
              <div className="absolute inset-0 bg-gradient-to-br from-purple-400/20 to-blue-400/20 rounded-xl blur-sm group-hover:blur-md transition-all duration-500" />
            </div>
            <div className="flex flex-col">
              <span className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-purple-400 via-purple-300 to-blue-400 bg-clip-text text-transparent group-hover:from-purple-300 group-hover:to-blue-300 transition-all duration-500">
                متجر بنتاكون
              </span>
              <span className="text-xs text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                خدمات الألعاب الرقمية
              </span>
            </div>
          </Link>

          {/* Enhanced Desktop Navigation */}
          <nav
            className="hidden lg:flex items-center space-x-2 space-x-reverse"
            role="navigation"
            aria-label="التنقل الرئيسي"
          >
            {navItems.map((item, index) => (
              <Link
                key={item.href}
                href={item.href}
                className="nav-link-modern interactive-element hover-lift text-gray-200 hover:text-white font-medium text-sm xl:text-base"
                style={{ animationDelay: `${index * 0.1}s` }}
                data-tooltip={item.label}
              >
                {item.label}
              </Link>
            ))}

            {/* Admin Panel Link with Enhanced Styling */}
            {mockUser.role === "admin" && (
              <Link
                href="/admin"
                className="nav-link-modern interactive-element hover-lift text-gray-200 hover:text-white font-medium text-sm xl:text-base flex items-center space-x-2 space-x-reverse bg-gradient-to-r from-purple-600/10 to-blue-600/10 border border-purple-500/20 animate-glow micro-bounce"
                style={{ animationDelay: `${navItems.length * 0.1}s` }}
                aria-label="لوحة التحكم - صفحة الإدارة"
                data-tooltip="لوحة التحكم"
              >
                <Shield className="w-4 h-4 text-purple-400 transition-transform duration-300 group-hover:rotate-12" />
                <span>لوحة التحكم</span>
                <ChevronDown className="w-3 h-3 opacity-60 transition-transform duration-300 group-hover:rotate-180" />
              </Link>
            )}
          </nav>

          {/* Enhanced User Actions */}
          <div className="hidden md:flex items-center space-x-3 space-x-reverse">
            {mockUser.isLoggedIn ? (
              <>
                {/* Wallet Balance with Enhanced Glassmorphism */}
                <div className="glass-button interactive-element hover-lift micro-bounce flex items-center space-x-2 space-x-reverse px-4 py-2 rounded-xl group animate-slide-in-top cursor-pointer" data-tooltip="رصيد المحفظة">
                  <div className="relative">
                    <Wallet className="w-4 h-4 text-emerald-400 group-hover:text-emerald-300 transition-all duration-300 group-hover:scale-110" />
                    <div className="absolute inset-0 bg-emerald-400/20 rounded-full blur-sm group-hover:blur-md transition-all duration-300" />
                  </div>
                  <span className="text-sm font-semibold text-gray-100 group-hover:text-white transition-colors duration-300">
                    ${mockUser.walletBalance.toFixed(2)}
                  </span>
                  <div className="w-1 h-1 bg-emerald-400 rounded-full micro-pulse" />
                </div>

                {/* User Profile with Enhanced Design */}
                <div className="glass-button interactive-element hover-lift micro-bounce flex items-center space-x-3 space-x-reverse px-4 py-2 rounded-xl group animate-slide-in-top cursor-pointer" style={{ animationDelay: '0.1s' }} data-tooltip="الملف الشخصي">
                  <div className="relative">
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-purple-500/40 transition-all duration-300 group-hover:scale-110">
                      <User className="w-4 h-4 text-white transition-transform duration-300 group-hover:rotate-12" />
                    </div>
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-800 micro-pulse" />
                  </div>
                  <div className="flex flex-col">
                    <span className="text-sm font-medium text-gray-100 group-hover:text-white transition-colors duration-300">
                      {mockUser.name}
                    </span>
                    <span className="text-xs text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                      متصل الآن
                    </span>
                  </div>
                  <ChevronDown className="w-3 h-3 text-gray-400 group-hover:text-gray-300 transition-all duration-300 group-hover:rotate-180" />
                </div>
              </>
            ) : (
              <button
                className="btn-primary interactive-element hover-lift micro-bounce shadow-lg hover:shadow-purple-500/25 animate-scale-in px-6 py-2.5 rounded-xl font-semibold text-sm transition-all duration-300 hover:scale-105"
                aria-label="تسجيل الدخول إلى الحساب"
                data-tooltip="تسجيل الدخول"
              >
                تسجيل الدخول
              </button>
            )}
          </div>

          {/* Enhanced Mobile Menu Button */}
          <button
            className="lg:hidden glass-button interactive-element hover-lift micro-bounce p-3 rounded-xl transition-all duration-300 hover:scale-105 active:scale-95 mobile-menu-container"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label={isMenuOpen ? "إغلاق القائمة" : "فتح القائمة"}
            aria-expanded={isMenuOpen}
            aria-controls="mobile-menu"
            data-tooltip={isMenuOpen ? "إغلاق" : "القائمة"}
          >
            <div className="relative w-6 h-6">
              <span className={`absolute inset-0 transition-all duration-300 ${isMenuOpen ? 'rotate-45 opacity-0' : 'rotate-0 opacity-100'}`}>
                <Menu className="w-6 h-6 text-gray-200 transition-transform duration-300 hover:scale-110" />
              </span>
              <span className={`absolute inset-0 transition-all duration-300 ${isMenuOpen ? 'rotate-0 opacity-100' : '-rotate-45 opacity-0'}`}>
                <X className="w-6 h-6 text-gray-200 transition-transform duration-300 hover:scale-110" />
              </span>
            </div>
          </button>
        </div>

        {/* Enhanced Mobile Menu */}
        <div
          className={`
            lg:hidden overflow-hidden transition-all duration-500 ease-out mobile-menu-container
            ${isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}
          `}
          id="mobile-menu"
          role="navigation"
          aria-label="قائمة التنقل المحمول"
        >
          <div className="glass-menu mt-4 p-6 rounded-2xl mx-4 animate-slide-in-top">
            <nav className="flex flex-col space-y-1">
              {navItems.map((item, index) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="nav-link-modern text-gray-200 hover:text-white font-medium py-3 px-4 rounded-xl transition-all duration-300 hover:bg-white/5 animate-stagger"
                  style={{ animationDelay: `${index * 0.1}s` }}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}

              {/* Admin Panel Link for Mobile */}
              {mockUser.role === "admin" && (
                <Link
                  href="/admin"
                  className="nav-link-modern text-gray-200 hover:text-white font-medium py-3 px-4 rounded-xl transition-all duration-300 hover:bg-white/5 flex items-center space-x-2 space-x-reverse bg-gradient-to-r from-purple-600/10 to-blue-600/10 border border-purple-500/20 animate-stagger"
                  style={{ animationDelay: `${navItems.length * 0.1}s` }}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Shield className="w-4 h-4 text-purple-400" />
                  <span>لوحة التحكم</span>
                </Link>
              )}

              {/* User Info for Mobile */}
              {mockUser.isLoggedIn && (
                <div className="mt-4 pt-4 border-t border-gray-700/30">
                  <div className="glass-button flex items-center space-x-3 space-x-reverse p-4 rounded-xl animate-stagger" style={{ animationDelay: `${(navItems.length + 1) * 0.1}s` }}>
                    <div className="relative">
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center shadow-lg">
                        <User className="w-5 h-5 text-white" />
                      </div>
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-800" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-100">{mockUser.name}</span>
                        <div className="flex items-center space-x-1 space-x-reverse text-emerald-400">
                          <Wallet className="w-4 h-4" />
                          <span className="text-sm font-semibold">${mockUser.walletBalance.toFixed(2)}</span>
                        </div>
                      </div>
                      <span className="text-xs text-gray-400">متصل الآن</span>
                    </div>
                  </div>
                </div>
              )}
            </nav>
          </div>
        </div>
      </div>
    </header>
  )
}
