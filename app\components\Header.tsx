"use client"

import Link from "next/link"
import { useState } from "react"
import { User, Menu, X, Wallet, Shield } from "lucide-react"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  // TODO: Replace with actual user data from Supabase
  const mockUser = {
    name: "أحمد محمد",
    walletBalance: 150.0,
    isLoggedIn: true,
    role: "admin", // Change this to test different roles
  }

  return (
    <header className="bg-gray-800/80 backdrop-blur-md border-b border-gray-700/50 sticky top-0 z-50 shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 space-x-reverse group">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
              <span className="text-white font-bold text-lg">ب</span>
            </div>
            <span className="text-lg sm:text-xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              متجر بنتاكون
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
            <Link href="/" className="hover:text-purple-400 transition-all duration-300 relative group">
              الرئيسية
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-full"></span>
            </Link>
            <Link href="/shop" className="hover:text-purple-400 transition-all duration-300 relative group">
              المتجر
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-full"></span>
            </Link>
            <Link href="/wallet" className="hover:text-purple-400 transition-all duration-300 relative group">
              المحفظة
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-full"></span>
            </Link>
            <Link href="/profile" className="hover:text-purple-400 transition-all duration-300 relative group">
              الملف الشخصي
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-full"></span>
            </Link>
            {mockUser.role === "admin" && (
              <Link
                href="/admin"
                className="hover:text-purple-400 transition-all duration-300 relative group flex items-center space-x-1 space-x-reverse"
              >
                <Shield className="w-4 h-4" />
                <span>لوحة التحكم</span>
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-full"></span>
              </Link>
            )}
          </nav>

          {/* User Actions */}
          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            {mockUser.isLoggedIn ? (
              <>
                <div className="flex items-center space-x-2 space-x-reverse bg-gray-700/50 backdrop-blur-sm px-3 py-1 rounded-lg border border-gray-600/50 shadow-lg">
                  <Wallet className="w-4 h-4 text-green-400" />
                  <span className="text-sm font-semibold">${mockUser.walletBalance.toFixed(2)}</span>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse bg-gray-700/50 backdrop-blur-sm px-3 py-1 rounded-lg border border-gray-600/50 shadow-lg">
                  <User className="w-4 h-4" />
                  <span className="text-sm">{mockUser.name}</span>
                </div>
              </>
            ) : (
              <button className="btn-primary shadow-lg hover:shadow-purple-500/25">تسجيل الدخول</button>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2 rounded-lg bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 hover:bg-gray-600/50 transition-all duration-300"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-700/50 bg-gray-800/90 backdrop-blur-md rounded-b-lg mt-2 shadow-xl animate-slide-down">
            <nav className="flex flex-col space-y-4">
              <Link href="/" className="hover:text-purple-400 transition-colors px-2 py-1 rounded hover:bg-gray-700/50">
                الرئيسية
              </Link>
              <Link
                href="/shop"
                className="hover:text-purple-400 transition-colors px-2 py-1 rounded hover:bg-gray-700/50"
              >
                المتجر
              </Link>
              <Link
                href="/wallet"
                className="hover:text-purple-400 transition-colors px-2 py-1 rounded hover:bg-gray-700/50"
              >
                المحفظة
              </Link>
              <Link
                href="/profile"
                className="hover:text-purple-400 transition-colors px-2 py-1 rounded hover:bg-gray-700/50"
              >
                الملف الشخصي
              </Link>
              {mockUser.role === "admin" && (
                <Link
                  href="/admin"
                  className="hover:text-purple-400 transition-colors px-2 py-1 rounded hover:bg-gray-700/50 flex items-center space-x-1 space-x-reverse"
                >
                  <Shield className="w-4 h-4" />
                  <span>لوحة التحكم</span>
                </Link>
              )}
              {mockUser.isLoggedIn && (
                <div className="flex items-center space-x-2 space-x-reverse bg-gray-700/50 backdrop-blur-sm px-3 py-2 rounded-lg w-fit border border-gray-600/50">
                  <Wallet className="w-4 h-4 text-green-400" />
                  <span className="text-sm font-semibold">${mockUser.walletBalance.toFixed(2)}</span>
                </div>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
