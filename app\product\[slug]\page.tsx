"use client"

import { useState } from "react"
import Image from "next/image"
import { Star, ShoppingCart, Check, Key, AlertTriangle } from "lucide-react"
import { mockProducts } from "../../data/mockData"
import type { Package } from "../../types"

interface ProductPageProps {
  params: {
    slug: string
  }
}

export default function ProductPage({ params }: ProductPageProps) {
  // TODO: Replace with Supabase data fetching
  const product = mockProducts.find((p) => p.slug === params.slug)

  const [selectedPackage, setSelectedPackage] = useState<Package | null>(null)
  const [customFieldValues, setCustomFieldValues] = useState<Record<string, string>>({})
  const [dropdownValues, setDropdownValues] = useState<Record<string, string>>({})
  const [isOrdering, setIsOrdering] = useState(false)

  // TODO: Get user role from Supabase auth
  const userRole = "user" // This would determine pricing

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-2xl font-bold mb-4">المنتج غير موجود</h1>
        <p className="text-gray-400">المنتج الذي تبحث عنه غير موجود.</p>
      </div>
    )
  }

  const handleOrder = async () => {
    if (!selectedPackage) return

    setIsOrdering(true)

    // TODO: Implement order creation with Supabase
    // TODO: Assign available digital code to order if package has codes
    // TODO: Mark code as used and bind to orderId
    await new Promise((resolve) => setTimeout(resolve, 2000))

    alert("تم تقديم الطلب بنجاح! ستحصل على العناصر قريباً.")
    setIsOrdering(false)
  }

  const isFormValid = () => {
    if (!selectedPackage) return false

    // Check if package is out of stock (for digital codes)
    if (selectedPackage.hasDigitalCodes && (selectedPackage.availableCodesCount || 0) === 0) {
      return false
    }

    // Check required custom fields
    const requiredFields = product.customFields?.filter((field) => field.required) || []
    for (const field of requiredFields) {
      if (!customFieldValues[field.id]) return false
    }

    // Check required dropdowns
    const requiredDropdowns = product.dropdowns?.filter((dropdown) => dropdown.required) || []
    for (const dropdown of requiredDropdowns) {
      if (!dropdownValues[dropdown.id]) return false
    }

    return true
  }

  // Get role-based pricing
  const getRolePrice = (pkg: Package) => {
    switch (userRole) {
      case "admin":
        return pkg.price * 0.7 // 30% discount for admin
      case "distributor":
        return pkg.price * 0.85 // 15% discount for distributor
      default:
        return pkg.price
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Product Image and Info */}
        <div>
          <div className="relative mb-6">
            <Image
              src={product.coverImage || "/placeholder.svg"}
              alt={product.title}
              width={600}
              height={400}
              className="w-full h-80 object-cover rounded-xl shadow-2xl"
            />
            {product.featured && (
              <div className="absolute top-4 left-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white px-3 py-1 rounded-lg font-semibold shadow-lg">
                مميز
              </div>
            )}
          </div>

          <h1 className="text-3xl md:text-4xl font-bold mb-4">{product.title}</h1>

          <div className="flex items-center space-x-6 space-x-reverse mb-6">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Star className="w-5 h-5 text-yellow-400 fill-current" />
              <span className="text-lg font-semibold">{product.rating}</span>
              <span className="text-gray-400">({product.commentCount} تقييم)</span>
            </div>
          </div>

          <div className="flex flex-wrap gap-2 mb-6">
            {product.tags.map((tag) => (
              <span
                key={tag}
                className="bg-gray-700/50 backdrop-blur-sm text-gray-300 px-3 py-1 rounded-lg text-sm border border-gray-600/50"
              >
                {tag}
              </span>
            ))}
          </div>

          <div className="prose prose-invert max-w-none">
            <p className="text-gray-300 text-lg leading-relaxed">{product.description}</p>
          </div>
        </div>

        {/* Order Form */}
        <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700/50 h-fit shadow-xl">
          <h2 className="text-2xl font-bold mb-6">اختر الحزمة</h2>

          {/* Package Selection */}
          <div className="grid grid-cols-1 gap-4 mb-6">
            {product.packages.map((pkg) => {
              const rolePrice = getRolePrice(pkg)
              const hasDiscount = rolePrice < pkg.price
              const isOutOfStock = pkg.hasDigitalCodes && (pkg.availableCodesCount || 0) === 0

              return (
                <div
                  key={pkg.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-all duration-300 backdrop-blur-sm relative ${
                    selectedPackage?.id === pkg.id
                      ? "border-purple-500 bg-purple-500/10 shadow-lg shadow-purple-500/20"
                      : isOutOfStock
                        ? "border-red-500/50 bg-red-500/5 cursor-not-allowed opacity-60"
                        : "border-gray-600/50 hover:border-gray-500 bg-gray-700/30"
                  }`}
                  onClick={() => !isOutOfStock && setSelectedPackage(pkg)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Image
                        src={pkg.image || "/placeholder.svg"}
                        alt={pkg.name}
                        width={50}
                        height={50}
                        className="rounded-lg"
                      />
                      <div>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <h3 className="font-semibold">{pkg.name}</h3>
                          {pkg.hasDigitalCodes && (
                            <div className="flex items-center space-x-1 space-x-reverse">
                              <Key className="w-4 h-4 text-blue-400" />
                              <span className="text-xs text-blue-400">كود رقمي</span>
                            </div>
                          )}
                        </div>
                        {pkg.description && <p className="text-sm text-gray-400">{pkg.description}</p>}
                        {pkg.hasDigitalCodes && (
                          <p className="text-xs text-gray-500 mt-1">
                            {isOutOfStock ? (
                              <span className="text-red-400 flex items-center space-x-1 space-x-reverse">
                                <AlertTriangle className="w-3 h-3" />
                                <span>نفدت الأكواد</span>
                              </span>
                            ) : (
                              `متوفر ${pkg.availableCodesCount} كود`
                            )}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        {hasDiscount && <span className="text-gray-400 line-through text-sm">${pkg.price}</span>}
                        <span className="text-lg font-bold text-purple-400">${rolePrice.toFixed(2)}</span>
                      </div>
                      {hasDiscount && (
                        <span className="text-green-400 text-sm">خصم {userRole === "admin" ? "المدير" : "الموزع"}</span>
                      )}
                    </div>
                  </div>
                  {selectedPackage?.id === pkg.id && !isOutOfStock && (
                    <div className="absolute right-2 top-2">
                      <Check className="w-5 h-5 text-purple-400" />
                    </div>
                  )}
                </div>
              )
            })}
          </div>

          {/* Digital Code Info */}
          {selectedPackage?.hasDigitalCodes && (
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mb-6">
              <div className="flex items-center space-x-2 space-x-reverse mb-2">
                <Key className="w-5 h-5 text-blue-400" />
                <h4 className="font-semibold text-blue-400">تسليم كود رقمي</h4>
              </div>
              <p className="text-sm text-blue-300">
                ستحصل على كود رقمي فوري بعد إتمام الدفع. يمكنك عرض الكود من صفحة المحفظة في قسم "تاريخ الطلبات".
              </p>
            </div>
          )}

          {/* Custom Fields */}
          {product.customFields && product.customFields.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-4">معلومات الحساب</h3>
              {product.customFields.map((field) => (
                <div key={field.id} className="mb-4">
                  <label className="block text-sm font-medium mb-2">
                    {field.label} {field.required && <span className="text-red-400">*</span>}
                  </label>
                  <input
                    type={field.type}
                    placeholder={field.placeholder}
                    value={customFieldValues[field.id] || ""}
                    onChange={(e) =>
                      setCustomFieldValues((prev) => ({
                        ...prev,
                        [field.id]: e.target.value,
                      }))
                    }
                    className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    required={field.required}
                  />
                </div>
              ))}
            </div>
          )}

          {/* Dropdowns */}
          {product.dropdowns && product.dropdowns.length > 0 && (
            <div className="mb-6">
              {product.dropdowns.map((dropdown) => (
                <div key={dropdown.id} className="mb-4">
                  <label className="block text-sm font-medium mb-2">
                    {dropdown.label} {dropdown.required && <span className="text-red-400">*</span>}
                  </label>
                  <select
                    value={dropdownValues[dropdown.id] || ""}
                    onChange={(e) =>
                      setDropdownValues((prev) => ({
                        ...prev,
                        [dropdown.id]: e.target.value,
                      }))
                    }
                    className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    required={dropdown.required}
                  >
                    <option value="">اختر {dropdown.label}</option>
                    {dropdown.options.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              ))}
            </div>
          )}

          {/* Order Button */}
          <button
            onClick={handleOrder}
            disabled={!isFormValid() || isOrdering}
            className={`w-full py-3 px-6 rounded-lg font-semibold text-lg transition-all duration-300 ${
              isFormValid() && !isOrdering
                ? "btn-primary shadow-lg hover:shadow-purple-500/25"
                : "bg-gray-600/50 text-gray-400 cursor-not-allowed"
            }`}
          >
            {isOrdering ? (
              <div className="flex items-center justify-center space-x-2 space-x-reverse">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>جاري المعالجة...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-2 space-x-reverse">
                <ShoppingCart className="w-5 h-5" />
                <span>اطلب الآن {selectedPackage && `- $${getRolePrice(selectedPackage).toFixed(2)}`}</span>
              </div>
            )}
          </button>

          {!isFormValid() && selectedPackage && (
            <p className="text-red-400 text-sm mt-2 text-center">
              {selectedPackage.hasDigitalCodes && (selectedPackage.availableCodesCount || 0) === 0
                ? "هذه الحزمة غير متاحة حالياً (نفدت الأكواد)"
                : "يرجى ملء جميع الحقول المطلوبة"}
            </p>
          )}
        </div>
      </div>
    </div>
  )
}
